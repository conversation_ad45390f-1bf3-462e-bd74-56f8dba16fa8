import { requestClient } from '#/api/request';

/**
 * 电力极值报表数据类型定义
 */
export interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: number; // 日期时间戳
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: number; // 最大值发生时间戳
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: number; // 最小值发生时间戳
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: number; // 最大值发生时间戳
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: number; // 最小值发生时间戳
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: number; // 最大值发生时间戳
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: number; // 最小值发生时间戳
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: number; // 最大值发生时间戳
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: number; // 最小值发生时间戳
  powerFactorAvgValue: number; // 平均值
}

/**
 * 电力极值报表查询参数
 */
export interface PowerExtremesParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  reportType?: 'dayly' | 'monthly' | 'custom'; // 报表类型
  powerType?: string; // 电力类别 (1:功率, 2:电流, 3:相电压, 4:线电压, 5:不平衡度, 6:电压谐波, 7:电流谐波)
  circuitName?: string; // 回路名称（用于搜索过滤）
  page?: number; // 页码
  pageSize?: number; // 每页数量
}

/**
 * 电力极值报表响应数据
 */
export interface PowerExtremesResponse {
  items: PowerExtremesData[];
  total: number;
  page: number;
  pageSize: number;
  reportType?: string;
}

/**
 * 获取电力极值报表数据
 * @param params 查询参数
 * @returns 电力极值报表数据
 */
export async function getPowerExtremesApi(
  params?: PowerExtremesParams,
): Promise<PowerExtremesResponse> {
  console.log('getPowerExtremesApi called with params:', params);

  return requestClient.get('/power/extremes', {
    params,
  });
}

/**
 * 导出电力极值报表数据
 * @param params 查询参数
 * @returns 导出文件流
 */
export async function exportPowerExtremesApi(
  params?: PowerExtremesParams,
): Promise<Blob> {
  console.log('exportPowerExtremesApi called with params:', params);

  return requestClient.get('/power/extremes/export', {
    params,
    responseType: 'blob',
  });
}
