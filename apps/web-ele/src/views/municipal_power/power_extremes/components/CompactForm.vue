<template>
  <div class="compact-form-wrapper">
    <!-- 表单项容器 - 使用 flexbox 实现紧密排列 -->
    <div class="compact-form-items">
      <slot />
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="compact-form-actions">
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup lang="ts">
// 紧凑表单布局组件
// 用于实现表单项的紧密排列，而不是占据整个网格列
</script>

<style scoped lang="scss">
.compact-form-wrapper {
  @apply w-full;
  
  .compact-form-items {
    // 使用 flexbox 实现表单项的紧密排列
    @apply flex flex-wrap items-center gap-x-6 gap-y-4;
    
    // 确保表单项不会换行（如果空间足够）
    @media (min-width: 1024px) {
      @apply flex-nowrap;
    }
    
    // 中等屏幕允许适当换行
    @media (max-width: 1023px) and (min-width: 768px) {
      @apply flex-wrap;
    }
    
    // 小屏幕垂直排列
    @media (max-width: 767px) {
      @apply flex-col items-stretch gap-y-3;
    }
  }
  
  .compact-form-actions {
    @apply flex items-center gap-2 mt-4;
    
    // 小屏幕下操作按钮也垂直排列
    @media (max-width: 767px) {
      @apply flex-col items-stretch gap-y-2;
    }
  }
}

// 覆盖 Vben Form 的默认网格布局
:deep(.vben-form) {
  // 移除默认的网格布局
  display: block !important;
  
  // 表单项容器使用 flexbox
  .vben-form-render {
    display: flex !important;
    flex-wrap: wrap;
    gap: 1.5rem 1.5rem;
    align-items: center;
    
    @media (max-width: 767px) {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }
  }
  
  // 表单项不占据固定宽度
  .vben-form-item {
    flex: 0 0 auto !important;
    width: auto !important;
    margin-bottom: 0 !important;
    
    // 确保表单项内容紧凑
    .vben-form-item-content {
      width: auto !important;
    }
  }
  
  // 操作按钮区域
  .vben-form-actions {
    flex: 1 1 100% !important;
    margin-top: 1rem;
    
    @media (max-width: 767px) {
      margin-top: 0.75rem;
    }
  }
}
</style>
