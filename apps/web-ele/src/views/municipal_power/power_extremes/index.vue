<script lang="ts" setup>
import type { PowerExtremesData, ReportType } from './data';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { $t } from '@vben/locales';

import dayjs from 'dayjs';
import { ElButton, ElTabPane, ElTabs } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchEnterprisesApi } from '#/api/energy/enterprise';
import { getPowerExtremesApi } from '#/api/power/extremes';

import { useColumns } from './data';
import { createFormOptions } from './form-config';

/* .左侧过滤筛选 */

const asideFormOptions = {};

const [asideForm, asideFormApi] = useVbenForm({
  // 默认展开搜索表单
  collapsed: false,
  // 所有表单项共用配置
  commonConfig: {
    // 所有表单项统一样式
    componentProps: {},
    // 控制表单控件的最大宽度，避免在网格布局中过大
    controlClass: 'max-w-xs', // 限制控件最大宽度为 max-w-xs (20rem/320px)
    // labelClass: 'justify-start',
    // labelWidth: 80,
    labelClass: 'ml-4',
    // formItemClass: 'flex-nowrap',
  },
  // 根据报表类型获取对应的schema配置
  schema: [
    {
      component: 'SearchSelector',
      fieldName: 'enterprise',
      label: '企业',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm px-4',
        placeholder: $t('system.energyReport.form.enterprisePlaceholder'),
        searchPlaceholder: $t(
          'system.energyReport.form.enterpriseSearchPlaceholder',
        ),
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 270,
        filterable: true,
        remote: false, // 设置为 false，这样组件会在挂载时自动加载数据
        // 使用异步数据源获取企业列表
        dataSource: async (keyword?: string) => {
          try {
            // 调用企业搜索API，当 keyword 为空或 undefined 时，获取所有企业列表
            const result = await searchEnterprisesApi(keyword || undefined);
            return result;
          } catch (error) {
            console.error('获取企业列表失败:', error);
            // 如果API调用失败，返回模拟数据作为降级方案
            const mockData = [
              { label: '北京新能源科技有限公司', value: 'enterprise_001' },
              { label: '上海绿色电力集团', value: 'enterprise_002' },
              { label: '深圳智慧能源股份公司', value: 'enterprise_003' },
              { label: '广州清洁能源发展公司', value: 'enterprise_004' },
              { label: '杭州可再生能源企业', value: 'enterprise_005' },
            ];

            // 如果有搜索关键词，进行过滤
            if (keyword && keyword.trim()) {
              return mockData.filter((item) =>
                item.label.toLowerCase().includes(keyword.toLowerCase()),
              );
            }
            console.log(
              'Using fallback enterprise data:',
              mockData.length,
              'items',
            );
            return mockData;
          }
        },
        // 监听企业选择变化
        onChange: (value: number | string, option: any) => {},
      },
    },
    {
      component: 'Select',
      defaultValue: '1', // 默认选中第一项：功率
      componentProps: (values, formApi) => ({
        allowClear: false,
        options: [
          {
            label: '电',
            value: '1',
          },
        ],
        placeholder: '请选择',
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm px-4',
      }),
      fieldName: 'powerType',
      label: '能源类型',
    },
    {
      component: 'SearchSelector',
      fieldName: 'site',
      label: '站点名称',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm px-4',
        placeholder: '',
        searchPlaceholder: '',
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 270,
        filterable: true,
        remote: false, // 设置为 false，这样组件会在挂载时自动加载数据
        // 使用异步数据源获取企业列表
        dataSource: async (keyword?: string) => {
          try {
            // 调用企业搜索API，当 keyword 为空或 undefined 时，获取所有企业列表
            // const result = await searchEnterprisesApi(keyword || undefined);
            return [
              { label: '微电网研究院', value: '1' },
              { label: '微电网研究院-光伏', value: '2' },
              { label: '风电站', value: '3' },
              { label: '研究院储能', value: '4' },
            ];
          } catch (error) {
            console.error('获取企业列表失败:', error);
            // 如果API调用失败，返回模拟数据作为降级方案
            const mockData = [];
            // 如果有搜索关键词，进行过滤
            if (keyword && keyword.trim()) {
              return mockData.filter((item) =>
                item.label.toLowerCase().includes(keyword.toLowerCase()),
              );
            }
            return mockData;
          }
        },
        // 监听企业选择变化
        onChange: (value: number | string, option: any) => {},
      },
    },
  ],
  showDefaultActions: false,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单布局
  layout: 'vertical',
  // 使用自定义类名，通过 CSS 覆盖实现 flexbox 布局
  wrapperClass: 'grid-cols-1',
});

// 当前激活的标签页
const activeTab = ref('dayly');

// 不再需要单独的数据和加载状态映射，通过 GridApi 管理

// 查询参数存储
const daylyQueryParams = ref({});
const monthlyQueryParams = ref({});
const customQueryParams = ref({});

// 电力类别映射
const powerTypeMap = {
  '1': '功率',
  '2': '电流',
  '3': '相电压',
  '4': '线电压',
  '5': '不平衡度',
  '6': '电压谐波',
  '7': '电流谐波',
};

// 处理查询参数的工具函数
function getQueryParams(reportType: ReportType, formData: any) {
  // 获取电力类别，默认为功率
  const powerType = formData.powerType || '1';
  console.log(
    `${reportType} 电力类别: ${powerType} (${powerTypeMap[powerType as keyof typeof powerTypeMap]})`,
  );

  if (reportType === 'dayly') {
    const date = formData.date || dayjs().format('YYYY-MM-DD');
    return {
      startTime: date,
      endTime: date,
      powerType,
    };
  }

  if (reportType === 'monthly') {
    const month = formData.month || dayjs().format('YYYY-MM');
    const startTime = `${month}-01`;
    const endTime = dayjs(`${month}-01`).endOf('month').format('YYYY-MM-DD');
    return {
      startTime,
      endTime,
      powerType,
    };
  }

  if (reportType === 'custom') {
    const dateRange = formData.dateRange;
    if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
      return {
        startTime: dateRange[0],
        endTime: dateRange[1],
        powerType,
      };
    }
    // 默认最近7天
    return {
      startTime: dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
      powerType,
    };
  }

  return { startTime: '', endTime: '', powerType };
}

// 创建基础表格配置（不设置初始数据，通过 API 动态设置）
const createGridOptions = (reportType: ReportType) => {
  console.log(`Creating grid options for ${reportType} with scroll config`);

  return {
    columns: useColumns(reportType),
    height: 'auto',
    keepSource: true,
    showFooter: false,
    // 不设置 loading 和 data，通过 API 动态设置
    // 表格自适应配置
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
    },
    // 表格布局配置
    border: true,
    stripe: true,
    // 滚动配置 - 关键配置！
    showOverflow: true, // 显示溢出内容，启用水平滚动
    scrollX: {
      enabled: true, // 启用水平滚动
    },
    scrollY: {
      enabled: true, // 启用垂直滚动
    },
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      keyField: 'date',
    },
  } as VxeTableGridOptions<PowerExtremesData>;
};

// 表单提交处理函数
function handleDaylySubmit(values: any) {
  console.log('日报表单提交:', values);
  daylyQueryParams.value = values;
  fetchTableData('dayly');
}

function handleMonthlySubmit(values: any) {
  console.log('月报表单提交:', values);
  monthlyQueryParams.value = values;
  fetchTableData('monthly');
}

function handleCustomSubmit(values: any) {
  console.log('自定义报表表单提交:', values);
  customQueryParams.value = values;
  fetchTableData('custom');
}

// 创建表单组件（获取表单 API 引用）
const [DaylyQueryForm, daylyFormApi] = useVbenForm({
  ...createFormOptions('dayly'),
  handleSubmit: handleDaylySubmit,
});

const [MonthlyQueryForm, monthlyFormApi] = useVbenForm({
  ...createFormOptions('monthly'),
  handleSubmit: handleMonthlySubmit,
});

const [CustomQueryForm] = useVbenForm({
  ...createFormOptions('custom'),
  handleSubmit: handleCustomSubmit,
});
// 创建表格组件
const [DaylyGrid, daylyGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions('dayly'),
});

const [MonthlyGrid, monthlyGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions('monthly'),
});

const [CustomGrid, customGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions('custom'),
});

// 数据查询函数
const fetchTableData = async (reportType: ReportType) => {
  // 获取对应的 GridApi 和表单数据
  let gridApi: any;
  let formData: any;

  switch (reportType) {
    case 'custom': {
      gridApi = customGridApi;
      formData = customQueryParams.value;

      break;
    }
    case 'dayly': {
      gridApi = daylyGridApi;
      formData = daylyQueryParams.value;

      break;
    }
    case 'monthly': {
      gridApi = monthlyGridApi;
      formData = monthlyQueryParams.value;

      break;
    }
    // No default
  }

  if (!gridApi) {
    console.error(`GridApi for ${reportType} not found`);
    return;
  }

  // 设置加载状态
  gridApi.setGridOptions({ loading: true });

  try {
    // 处理查询参数（包含时间和电力类别）
    const { startTime, endTime, powerType } = getQueryParams(
      reportType,
      formData,
    );

    const params = {
      startTime,
      endTime,
      powerType,
      reportType,
      page: 1,
      pageSize: 50,
    };

    console.log(`${reportType} query params (含电力类别):`, params);

    const response = await getPowerExtremesApi(params);
    console.log(`${reportType} API response:`, response);

    // 通过 GridApi 设置表格数据
    const tableData = response.items || [];
    gridApi.setGridOptions({
      data: tableData,
      loading: false,
    });

    console.log(`${reportType} table data updated:`, tableData.length, 'items');
  } catch (error) {
    console.error(`Failed to fetch ${reportType} data:`, error);
    // 设置空数据和关闭加载状态
    gridApi.setGridOptions({
      data: [],
      loading: false,
    });
  }
};

// 刷新当前标签页数据
function onRefresh() {
  const currentTab = activeTab.value as ReportType;
  fetchTableData(currentTab);
}

// 日期切换处理函数
function handleDateChange(direction: 'next' | 'prev') {
  const currentDate = dayjs(
    daylyQueryParams.value.date || dayjs().format('YYYY-MM-DD'),
  );
  const newDate =
    direction === 'prev'
      ? currentDate.subtract(1, 'day')
      : currentDate.add(1, 'day');

  // 简单的边界检查：不允许选择未来日期
  if (direction === 'next' && newDate.isAfter(dayjs(), 'day')) {
    console.log('不能选择未来日期');
    return;
  }

  const newDateStr = newDate.format('YYYY-MM-DD');
  console.log(`切换日期: ${currentDate.format('YYYY-MM-DD')} -> ${newDateStr}`);

  // 更新日报查询参数
  daylyQueryParams.value = {
    ...daylyQueryParams.value,
    date: newDateStr,
  };

  // 同步更新表单字段
  daylyFormApi.setFieldValue('date', newDateStr);

  // 重新查询数据
  fetchTableData('dayly');
}

// 月份切换处理函数
function handleMonthChange(direction: 'next' | 'prev') {
  const currentMonth = dayjs(
    monthlyQueryParams.value.month || dayjs().format('YYYY-MM'),
  );
  const newMonth =
    direction === 'prev'
      ? currentMonth.subtract(1, 'month')
      : currentMonth.add(1, 'month');

  // 简单的边界检查：不允许选择未来月份
  if (direction === 'next' && newMonth.isAfter(dayjs(), 'month')) {
    console.log('不能选择未来月份');
    return;
  }

  const newMonthStr = newMonth.format('YYYY-MM');
  console.log(`切换月份: ${currentMonth.format('YYYY-MM')} -> ${newMonthStr}`);

  // 更新月报查询参数
  monthlyQueryParams.value = {
    ...monthlyQueryParams.value,
    month: newMonthStr,
  };

  // 同步更新表单字段
  monthlyFormApi.setFieldValue('month', newMonthStr);

  // 重新查询数据
  fetchTableData('monthly');
}

// 页面初始化时加载数据
onMounted(() => {
  // 初始化默认查询参数（包含电力类别）
  daylyQueryParams.value = {
    date: dayjs().format('YYYY-MM-DD'),
    powerType: '1', // 默认选择功率
  };
  monthlyQueryParams.value = {
    month: dayjs().format('YYYY-MM'),
    powerType: '1', // 默认选择功率
  };
  customQueryParams.value = {
    dateRange: [
      dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
    ],
    powerType: '1', // 默认选择功率
  };

  // 初始化时加载所有三个标签页的数据
  console.log('页面初始化，开始加载所有标签页数据...');
  console.log('日报参数:', daylyQueryParams.value);
  console.log('月报参数:', monthlyQueryParams.value);
  console.log('自定义参数:', customQueryParams.value);

  fetchTableData('dayly');
  fetchTableData('monthly');
  fetchTableData('custom');
});
</script>
<template>
  <Page auto-content-height>
    <div class="flex h-full gap-4">
      <div class="bg-card border-border h-full w-[300px] shrink-0 border py-4">
        <asideForm />
      </div>
      <div class="h-full w-[calc(100%-324px)]">
        <ElTabs v-model="activeTab" type="border-card" class="report-tabs">
          <ElTabPane label="日报" name="dayly">
            <div class="px-2">
              <DaylyQueryForm>
                <template #reset-before>
                  <ElButton
                    class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                    type="primary"
                    @click="() => handleDateChange('prev')"
                  >
                    <IconifyIcon icon="lucide:chevron-left" class="h-4 w-4" />
                    上一日
                  </ElButton>
                  <ElButton
                    class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                    type="primary"
                    @click="() => handleDateChange('next')"
                  >
                    下一日
                    <IconifyIcon icon="lucide:chevron-right" class="h-4 w-4" />
                  </ElButton>
                </template>
              </DaylyQueryForm>
            </div>
            <DaylyGrid class="h-[calc(100%-60px)]" />
          </ElTabPane>
          <ElTabPane label="月报" name="monthly">
            <div class="px-2">
              <MonthlyQueryForm>
                <template #reset-before>
                  <ElButton
                    class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                    type="primary"
                    @click="() => handleMonthChange('prev')"
                  >
                    <IconifyIcon icon="lucide:chevron-left" class="h-4 w-4" />
                    上一月
                  </ElButton>
                  <ElButton
                    class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                    type="primary"
                    @click="() => handleMonthChange('next')"
                  >
                    下一月
                    <IconifyIcon icon="lucide:chevron-right" class="h-4 w-4" />
                  </ElButton>
                </template>
              </MonthlyQueryForm>
            </div>

            <MonthlyGrid class="h-[calc(100%-60px)]" />
          </ElTabPane>
          <ElTabPane label="自定义" name="custom">
            <div class="px-2">
              <CustomQueryForm>
                <template #reset-before></template>
              </CustomQueryForm>
            </div>
            <CustomGrid class="h-[calc(100%-60px)]" />
          </ElTabPane>
        </ElTabs>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="scss">
// ElTabs 样式优化
.report-tabs {
  @apply h-full;

  // 标签页导航样式
  :deep(.el-tabs__nav-wrap) {
    @apply bg-background border-border border-r;
  }

  :deep(.el-tabs__nav) {
    @apply bg-background;
  }

  :deep(.el-tabs__item) {
    @apply text-foreground transition-colors duration-200;

    &:hover {
      @apply text-primary;
    }

    &.is-active {
      @apply text-primary font-medium;
    }
  }

  // 标签页内容区域
  :deep(.el-tabs__content) {
    @apply h-full overflow-hidden;
  }

  :deep(.el-tab-pane) {
    @apply h-full;
  }
}
// 响应式设计
@media (max-width: 768px) {
  :deep(.vxe-table) {
    font-size: 12px;
  }
}
</style>
<style lang="scss">
// 强制覆盖 Vben Form 的硬编码 grid 布局
.compact-form-layout {
  // 使用 !important 强制覆盖硬编码的 grid 类
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;

  // 大屏幕不换行，表单项水平排列
  @media (min-width: 1200px) {
    flex-wrap: nowrap !important;
    gap: 1rem 3rem !important;
  }

  // 中等屏幕适当换行
  @media (max-width: 1199px) and (min-width: 768px) {
    gap: 1rem 2rem !important;
  }

  // 小屏幕垂直排列
  @media (max-width: 767px) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }

  // 表单项容器样式
  > * {
    flex: 0 0 auto !important;
    margin-bottom: 0 !important;

    // 小屏幕下占满宽度
    @media (max-width: 767px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  // 操作按钮区域特殊处理 - 紧贴表单项，空间不够时自动换行
  > *:last-child {
    flex: 0 1 auto !important; // 允许收缩，空间不够时换行
    width: auto !important; // 覆盖 w-full 的 width: 100%
    min-width: fit-content !important; // 最小宽度为内容宽度

    // 强制覆盖组件内联样式 - 使用更高优先级
    grid-column: unset !important; // 覆盖 col-span-full 和内联样式
    margin-left: 0 !important; // 覆盖内联样式的 margin-left: auto

    // 针对内部的 form-actions 组件
    &.w-full {
      width: auto !important; // 覆盖组件自身的 w-full
    }

    &.col-span-full {
      grid-column: unset !important; // 覆盖 col-span-full 类
    }

    &.text-right {
      text-align: left !important; // 覆盖右对齐
    }

    .w-full {
      width: auto !important; // 覆盖组件内部的 w-full
    }

    @media (max-width: 767px) {
      flex: 1 1 100% !important; // 小屏幕下独占一行
      width: 100% !important; // 小屏幕下恢复全宽
      grid-column: 1 / -1 !important; // 小屏幕下恢复全列
      margin-left: auto !important; // 小屏幕下恢复右对齐

      &.w-full {
        width: 100% !important;
      }

      &.text-right {
        text-align: right !important; // 小屏幕下恢复右对齐
      }

      .w-full {
        width: 100% !important;
      }
    }
  }

  // 确保表单控件保持合适的宽度
  .el-select,
  .el-date-editor,
  .el-input,
  .el-cascader {
    min-width: 180px !important; // 设置最小宽度
    width: auto !important;
  }

  // 日期范围选择器需要更大的宽度
  .el-date-editor--daterange,
  .el-date-editor--datetimerange {
    min-width: 280px !important;
  }

  // 下拉选择器特殊处理
  .el-select {
    .el-input {
      min-width: 180px !important;
    }
  }
}

// 更强的选择器，确保覆盖硬编码的 grid 类
div.compact-form-layout.grid {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;
}
</style>
