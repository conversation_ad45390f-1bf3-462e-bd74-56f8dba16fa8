<script lang="ts" setup>
import { ref } from 'vue';
import SearchSelector from '@/components/services/search-selector/SearchSelector.vue';

// 测试数据
const selectedValue = ref('');

const staticOptions = [
  { label: '微电网研究院', value: '1' },
  { label: '微电网研究院-光伏', value: '2' },
  { label: '风电站', value: '3' },
  { label: '研究院储能', value: '4' },
  { label: '工业园区A', value: '5' },
  { label: '工业园区B', value: '6' },
  { label: '商业综合体', value: '7' },
  { label: '住宅小区', value: '8' },
];

// 动态数据源函数
const dynamicDataSource = async (keyword: string = '') => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const allOptions = [
    { label: '微电网研究院', value: 'dynamic-1' },
    { label: '微电网研究院-光伏', value: 'dynamic-2' },
    { label: '风电站', value: 'dynamic-3' },
    { label: '研究院储能', value: 'dynamic-4' },
    { label: '工业园区A', value: 'dynamic-5' },
    { label: '工业园区B', value: 'dynamic-6' },
    { label: '商业综合体', value: 'dynamic-7' },
    { label: '住宅小区', value: 'dynamic-8' },
    { label: '数据中心', value: 'dynamic-9' },
    { label: '制造工厂', value: 'dynamic-10' },
  ];

  if (!keyword) {
    return allOptions;
  }

  return allOptions.filter(option => 
    option.label.toLowerCase().includes(keyword.toLowerCase())
  );
};

const handleChange = (value: string | number, option: any) => {
  console.log('选择变更:', { value, option });
};

const handleSearch = (keyword: string) => {
  console.log('搜索关键词:', keyword);
};
</script>

<template>
  <div class="p-6 space-y-8">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold text-foreground">SearchSelector Radio 样式测试</h1>
      <p class="text-muted-foreground">
        测试 SearchSelector 组件使用 ElRadio 的新样式效果
      </p>
    </div>

    <!-- 静态选项测试 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-foreground">静态选项测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">默认样式</label>
          <SearchSelector
            v-model="selectedValue"
            :options="staticOptions"
            placeholder="请选择站点"
            button-text="选择站点"
            @change="handleChange"
            @search="handleSearch"
          />
          <p class="text-xs text-muted-foreground">
            当前选中: {{ selectedValue || '无' }}
          </p>
        </div>

        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">主要按钮样式</label>
          <SearchSelector
            v-model="selectedValue"
            :options="staticOptions"
            placeholder="请选择站点"
            button-text="选择站点"
            button-type="primary"
            @change="handleChange"
            @search="handleSearch"
          />
        </div>
      </div>
    </div>

    <!-- 动态数据源测试 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-foreground">动态数据源测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">远程搜索</label>
          <SearchSelector
            v-model="selectedValue"
            :data-source="dynamicDataSource"
            placeholder="请选择站点"
            button-text="远程搜索站点"
            remote
            @change="handleChange"
            @search="handleSearch"
          />
        </div>

        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">本地数据源</label>
          <SearchSelector
            v-model="selectedValue"
            :data-source="dynamicDataSource"
            placeholder="请选择站点"
            button-text="本地数据源"
            @change="handleChange"
            @search="handleSearch"
          />
        </div>
      </div>
    </div>

    <!-- 不同尺寸测试 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-foreground">不同尺寸测试</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">小尺寸</label>
          <SearchSelector
            v-model="selectedValue"
            :options="staticOptions.slice(0, 4)"
            placeholder="小尺寸"
            button-size="small"
            @change="handleChange"
          />
        </div>

        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">默认尺寸</label>
          <SearchSelector
            v-model="selectedValue"
            :options="staticOptions.slice(0, 4)"
            placeholder="默认尺寸"
            button-size="default"
            @change="handleChange"
          />
        </div>

        <div class="space-y-2">
          <label class="text-sm font-medium text-foreground">大尺寸</label>
          <SearchSelector
            v-model="selectedValue"
            :options="staticOptions.slice(0, 4)"
            placeholder="大尺寸"
            button-size="large"
            @change="handleChange"
          />
        </div>
      </div>
    </div>

    <!-- 样式预览 -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-foreground">样式效果预览</h2>
      <div class="p-4 border border-border rounded-lg bg-card">
        <p class="text-sm text-muted-foreground mb-4">
          点击下方按钮查看 Radio 样式的选项列表效果：
        </p>
        <SearchSelector
          v-model="selectedValue"
          :options="staticOptions"
          placeholder="查看 Radio 样式效果"
          button-text="点击查看 Radio 样式"
          button-type="primary"
          popover-width="320"
          @change="handleChange"
          @search="handleSearch"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 页面特定样式 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
</style>
